import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import { readFileContent, isSupportedFileType } from "@/utils/file-reader";

export const Route = createFileRoute("/test-upload")({
  component: TestUploadComponent,
});

function TestUploadComponent() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileContent, setFileContent] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>("");

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setSelectedFile(file);
    setFileContent("");
    setError("");

    // Check if file type is supported
    if (!isSupportedFileType(file)) {
      setError("不支持的文件类型。请选择 TXT、DOC、DOCX 或 PDF 文件。");
      return;
    }

    setIsLoading(true);

    try {
      console.log("开始读取文件:", file.name);
      const content = await readFileContent(file);
      setFileContent(content);
      console.log("文件读取成功，内容长度:", content.length);
    } catch (err) {
      console.error("文件读取失败:", err);
      setError(`文件读取失败: ${err instanceof Error ? err.message : "未知错误"}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">文件上传测试</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">选择文件</h2>
          <input
            type="file"
            accept=".txt,.doc,.docx,.pdf"
            onChange={handleFileSelect}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          
          {selectedFile && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-blue-700">
                <strong>已选择文件:</strong> {selectedFile.name} ({(selectedFile.size / 1024).toFixed(2)} KB)
              </p>
              <p className="text-blue-600 text-sm">
                <strong>文件类型:</strong> {selectedFile.type || "未知"}
              </p>
            </div>
          )}
          
          {isLoading && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-yellow-700">正在读取文件...</p>
            </div>
          )}
          
          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700">{error}</p>
            </div>
          )}
        </div>

        {fileContent && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">文件内容</h2>
            <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
              <pre className="whitespace-pre-wrap text-sm text-gray-700">
                {fileContent.length > 2000 
                  ? fileContent.substring(0, 2000) + "\n\n... (内容已截断，总长度: " + fileContent.length + " 字符)"
                  : fileContent
                }
              </pre>
            </div>
            <p className="text-sm text-gray-500 mt-2">
              总字符数: {fileContent.length}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
